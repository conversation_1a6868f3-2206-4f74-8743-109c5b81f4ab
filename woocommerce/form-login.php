<?php
/*
Template Name: Logowanie
*/

get_header(); ?>
<main>
    <div class="logowanie-container">
        <section class="logowanie">
            <h1 class="logowanie-header">Zaloguj się</h1>
            <p class="logowanie-description">Je<PERSON><PERSON> nie jesteś jeszcze naszym klientem i chcesz uzyskać dostęp do platformy B2B, <a href="<?php echo home_url('rejestracja') ?>">zarejestruj się tutaj!</a></p>
            <p class="logowanie-description">Jeżeli nie jesteś klientem firmowym, <a href="http://www.fitospa.pl">sprawdź nasz sklep online</a></p>

            <div class="logowanie-box">
                <?php if ( is_user_logged_in() ) : ?>
                    <p class="logowanie-message">
                        <?php esc_html_e('Je<PERSON><PERSON> już <PERSON>alogowany. Możesz przejść do <a href="' . esc_url(wc_get_account_endpoint_url('dashboard')) . '">panelu użytkownika</a>.', 'woocommerce'); ?>
                    </p>
                <?php else : ?>
                    <!-- Wyświetlenie komunikatów błędów logowania -->
                    <?php if (isset($_GET['login']) && $_GET['login'] == 'failed') : ?>
                        <div class="woocommerce-error">
                            <?php esc_html_e('Niepoprawny login lub hasło. Spróbuj ponownie.', 'woocommerce'); ?>
                        </div>
                    <?php endif; ?>
                    <?php
                    if (isset($_GET['registration']) && $_GET['registration'] == 'success') {
                        echo '<div class="woocommerce-message">' . esc_html('Rejestracja zakończona sukcesem! Możesz teraz zalogować się.', 'woocommerce') . '</div>';
                    }
                    ?>

                    <!-- Formularz logowania WooCommerce -->
                    <form class="woocommerce-form woocommerce-form-login login" method="post">
                        <div class="form-row form-row-username">
                            <label for="username"><?php esc_html_e('Email', 'woocommerce'); ?></label>
                            <input type="email" class="woocommerce-Input woocommerce-Input--text input-text" name="username" id="username" autocomplete="username" value="<?php echo ( ! empty( $_POST['username'] ) ) ? esc_attr( wp_unslash( $_POST['username'] ) ) : ''; ?>" required aria-required="true" />
                        </div>

                        <div class="form-row form-row-password">
                            <label for="password"><?php esc_html_e('Hasło', 'woocommerce'); ?></label>
                            <input type="password" class="woocommerce-Input woocommerce-Input--text input-text" name="password" id="password" autocomplete="current-password" required aria-required="true" />
                        </div>

                        <div class="form-row form-row-lost-password">
                            <a href="<?php echo esc_url( home_url('/odzyskiwanie-hasla/') ); ?>" class="lost-password-link"><?php esc_html_e('Nie pamiętasz hasła?', 'woocommerce'); ?></a>
                        </div>

                        <div class="form-row form-row-rememberme">
                            <label class="woocommerce-form__label woocommerce-form__label-for-checkbox woocommerce-form-login__rememberme">
                                <input class="woocommerce-form__input woocommerce-form__input-checkbox" name="rememberme" type="checkbox" id="rememberme" value="forever" />
                                <span><?php esc_html_e('Zapamiętaj mnie', 'woocommerce'); ?></span>
                            </label>
                        </div>

                        <div class="form-row form-row-submit">
                            <button type="submit" class="woocommerce-button button woocommerce-form-login__submit" name="login" value="<?php esc_attr_e( 'Zaloguj się', 'woocommerce' ); ?>"><?php esc_html_e( 'Zaloguj się', 'woocommerce' ); ?></button>
                        </div>

                        <input type="hidden" name="redirect" value="<?php echo esc_url( wc_get_page_permalink( 'myaccount' ) ); ?>" />
                        <?php do_action( 'woocommerce_login_form_end' ); ?>
                    </form>
                <?php endif; ?>

                <div class="form-row form-row-register">
                    <span class="register-link"><?php esc_html_e('Nie masz konta?', 'woocommerce'); ?> <a href="<?php echo home_url('rejestracja') ?>"><?php esc_html_e('Zarejestruj się', 'woocommerce'); ?></a></span>
                </div>
            </div>
        </section>
    </div>
</main>

<?php get_footer(); ?>


