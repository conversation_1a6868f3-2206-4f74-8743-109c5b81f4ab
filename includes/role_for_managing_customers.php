<?php

const DD_CUSTOMER_MANAGER_ROLE = 'customer_manager';
const DD_ROLES_ALLOWED_TO_CUSTOMER_MANAGER = ['customer'];

function dd_add_customer_manager_role() {
    $shop_manager = get_role('shop_manager');
    $capabilities = $shop_manager ? $shop_manager->capabilities : array();

    $additional_caps = array(
        'list_users' => true,
        'create_users' => true,
        'edit_users' => true,
        'delete_users' => false,
        'promote_users' => true,  // Potrzebne do zmiany ról
    );

    $all_capabilities = array_merge($capabilities, $additional_caps);

    if (!get_role(DD_CUSTOMER_MANAGER_ROLE)) {
        add_role(DD_CUSTOMER_MANAGER_ROLE, 'Zarządzanie klientami', $all_capabilities);
    }
}
add_action('init', 'dd_add_customer_manager_role');

// Ograniczenie dostępu do listy użytkowników
function dd_restrict_customer_manager_user_access($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    $current_user = wp_get_current_user();

    if (!in_array(DD_CUSTOMER_MANAGER_ROLE, $current_user->roles)) {
        return;
    }

    // Ukryj administratorów z listy użytkowników
    $query->set('role__not_in', array('administrator'));
}
add_action('pre_get_users', 'dd_restrict_customer_manager_user_access');

// Ograniczenie możliwości edycji profili administratorów
function dd_prevent_admin_profile_edit() {
    global $pagenow;

    if (($pagenow != 'user-edit.php' && $pagenow != 'profile.php') || !isset($_GET['user_id'])) {
        return;
    }

    $current_user = wp_get_current_user();

    if (!in_array(DD_CUSTOMER_MANAGER_ROLE, $current_user->roles)) {
        return;
    }

    $user_to_edit = get_user_by('id', intval($_GET['user_id']));

    if (!$user_to_edit) {
        return;
    }

    if (in_array('administrator', $user_to_edit->roles)) {
        wp_die('Nie masz uprawnień do edytowania kont administratorów.');
    }
}
add_action('admin_init', 'dd_prevent_admin_profile_edit');

// Ograniczenie dostępnych ról przy tworzeniu/edycji użytkowników
function dd_restrict_available_roles($roles) {
    $current_user = wp_get_current_user();

    if (!in_array(DD_CUSTOMER_MANAGER_ROLE, $current_user->roles)) {
        return $roles;
    }

    $allowed_roles = DD_ROLES_ALLOWED_TO_CUSTOMER_MANAGER;

    // Dodaj role zaczynające się od "client_"
    foreach ($roles as $role_key => $role_name) {
        if (str_starts_with($role_key, 'client_')) {
            $allowed_roles[] = $role_key;
        }
    }

    $filtered_roles = [];
    foreach ($allowed_roles as $role_key) {
        if (isset($roles[$role_key])) {
            $filtered_roles[$role_key] = $roles[$role_key];
        }
    }

    return $filtered_roles;
}
add_filter('editable_roles', 'dd_restrict_available_roles');

// Walidacja przy zapisywaniu użytkownika
function validate_user_role_assignment($errors, $update, $user) {
    $current_user = wp_get_current_user();

    if (!in_array(DD_CUSTOMER_MANAGER_ROLE, $current_user->roles)) {
        return $errors;
    }

    // Sprawdź czy próbuje przypisać niedozwoloną rolę
    if (isset($_POST['role'])) {
        $assigned_role = sanitize_text_field($_POST['role']);
        $allowed_roles = DD_ROLES_ALLOWED_TO_CUSTOMER_MANAGER;

        // Pobierz wszystkie role zaczynające się od "client_"
        $all_roles = wp_roles()->get_names();
        foreach ($all_roles as $role_key => $role_name) {
            if (str_starts_with($role_key, 'client_')) {
                $allowed_roles[] = $role_key;
            }
        }

        if (!in_array($assigned_role, $allowed_roles)) {
            $errors->add('role_error', 'Nie masz uprawnień do przypisania tej roli.');
        }
    }

    if (!$update || !$user->ID) {
        return $errors;
    }

    $user_to_edit = get_user_by('id', $user->ID);
    if ($user_to_edit && in_array('administrator', $user_to_edit->roles)) {
        $errors->add('admin_edit_error', 'Nie masz uprawnień do edytowania kont administratorów.');
    }

    return $errors;
}
add_filter('user_profile_update_errors', 'validate_user_role_assignment', 10, 3);

// Dodatkowe zabezpieczenie - ukryj możliwość promocji do administratora
function hide_admin_role_option() {
    $current_user = wp_get_current_user();

    if (!in_array(DD_CUSTOMER_MANAGER_ROLE, $current_user->roles)) {
        return;
    }

    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Ukryj opcję roli administrator w select
            $('#role option[value="administrator"]').remove();
        });
    </script>
    <?php
}
add_action('admin_footer-user-edit.php', 'hide_admin_role_option');
add_action('admin_footer-user-new.php', 'hide_admin_role_option');

// Dodaj komunikat informacyjny na stronie użytkowników
function add_customer_manager_notice() {
    $current_user = wp_get_current_user();

    // Early return jeśli użytkownik nie ma roli customer_manager
    if (!in_array(DD_CUSTOMER_MANAGER_ROLE, $current_user->roles)) {
        return;
    }

    $screen = get_current_screen();

    // Early return jeśli nie jesteśmy na odpowiedniej stronie
    if (!$screen || !in_array($screen->id, array('users', 'user-edit', 'user-new'))) {
        return;
    }

    ?>
    <div class="notice notice-info">
        <p><strong>Zarządzanie klientami:</strong> Możesz tworzyć i edytować tylko konta z rolami klientów. Konta administratorów są ukryte i chronione.</p>
    </div>
    <?php
}
add_action('admin_notices', 'add_customer_manager_notice');
