<?php
const DD_LAST_ACTIVITY = 'dd_last_activity';

add_action('init', 'dd_track_user_activity');

function dd_track_user_activity() {
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        $current_time = current_time('timestamp');

        // Zapisz ostatnią aktywność użytkownika
        update_user_meta($user_id, DD_LAST_ACTIVITY, $current_time);
    }
}

// Dodaj menu w panelu administracyjnym
add_action('admin_menu', 'dd_add_online_users_menu');

function dd_add_online_users_menu() {
    add_menu_page(
        'Kto jest online?',
        'Kto jest online?',
        'list_users',
        'online-users',
        'dd_display_online_users_page',
        'dashicons-groups',
        30
    );
}

// Funkcja wyświetlająca stronę z aktywnymi użytkownikami
function dd_display_online_users_page() {
    echo '<div class="wrap">';
    echo '<h1>Kto jest online?</h1>';
    echo '<p>Użytkownicy aktywni w ostatnich 24 godzinach:</p>';

    $online_users = dd_get_online_users();

    if (empty($online_users)) {
        echo '<p>Brak aktywnych użytkowników.</p>';
    } else {
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Adres e-mail</th>';
        echo '<th>Imię i nazwisko</th>';
        echo '<th>Ostatnia aktywność</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($online_users as $user) {
            $user_data = get_userdata($user->ID);
            $last_activity = get_user_meta($user->ID, DD_LAST_ACTIVITY, true);

            // Pobierz dane z WooCommerce
            $first_name = get_user_meta($user->ID, 'billing_first_name', true);
            $last_name = get_user_meta($user->ID, 'billing_last_name', true);

            // Jeśli brak danych WooCommerce, użyj danych WordPress
            if (empty($first_name)) {
                $first_name = get_user_meta($user->ID, 'first_name', true);
            }
            if (empty($last_name)) {
                $last_name = get_user_meta($user->ID, 'last_name', true);
            }

            $full_name = trim($first_name . ' ' . $last_name);
            if (empty($full_name)) {
                $full_name = $user_data->display_name;
            }

            $activity_time = $last_activity ? date('Y-m-d H:i:s', $last_activity) : 'Nieznana';

            echo '<tr>';
            echo '<td>' . esc_html($user_data->user_email) . '</td>';
            echo '<td>' . esc_html($full_name) . '</td>';
            echo '<td>' . esc_html($activity_time) . '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }

    echo '</div>';
}

// Funkcja pobierająca aktywnych użytkowników
function dd_get_online_users() {
    $current_time = current_time('timestamp');
    $last_day = $current_time - (1440 * 60); // 24h
    $args = array(
        'meta_query' => array(
            array(
                'key' => DD_LAST_ACTIVITY,
                'value' => $last_day,
                'compare' => '>='
            )
        )
    );

    $user_query = new WP_User_Query($args);
    return $user_query->get_results();
}

// Opcjonalnie: dodaj kolumnę z ostatnią aktywnością w liście użytkowników
add_filter('manage_users_columns', 'dd_add_last_activity_column');
add_action('manage_users_custom_column', 'dd_show_last_activity_column', 10, 3);

function dd_add_last_activity_column($columns) {
    $columns[DD_LAST_ACTIVITY] = 'Ostatnia aktywność';
    return $columns;
}

function dd_show_last_activity_column($value, $column_name, $user_id) {
    if ($column_name == DD_LAST_ACTIVITY) {
        $last_activity = get_user_meta($user_id, DD_LAST_ACTIVITY, true);
        if ($last_activity) {
            $current_time = current_time('timestamp');
            $time_diff = $current_time - $last_activity;

            if ($time_diff < 900) {
                return '<span style="color: green;">Online</span><br><small>' . date('Y-m-d H:i:s', $last_activity) . '</small>';
            } else {
                return '<span style="color: red;">Offline</span><br><small>' . date('Y-m-d H:i:s', $last_activity) . '</small>';
            }
        } else {
            return 'Nigdy';
        }
    }
    return $value;
}

// Wyczyść stare dane aktywności (opcjonalnie, uruchamiane codziennie)
add_action('wp_scheduled_delete', 'dd_cleanup_old_activity_data');

function dd_cleanup_old_activity_data() {
    global $wpdb;

    $one_week_ago = current_time('timestamp') - (7 * 24 * 60 * 60);

    $wpdb->query($wpdb->prepare(
        "DELETE FROM {$wpdb->usermeta} 
         WHERE meta_key = 'last_activity' 
         AND meta_value < %d",
        $one_week_ago
    ));
}
?>