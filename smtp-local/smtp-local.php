<?php
/*
Plugin Name: Obsługa lokalnego SMTP (Mailhog)
Description: Obsługa lokalnego SMTP (Mailhog)
Version: 1.0
Author: Digital Dimension
*/

add_action('phpmailer_init', function ($phpmailer) {

    $phpmailer->isSMTP();
    // host details
    $phpmailer->SMTPAuth = false;
    $phpmailer->SMTPSecure = '';
    $phpmailer->SMTPAutoTLS = false;
    $phpmailer->Host = getenv_docker('MAILHOG_HOST', '');
    $phpmailer->Port = '1025';
    // from details
    $phpmailer->From = '<EMAIL>';
    $phpmailer->FromName = 'Localhost admin';
    // login details
    $phpmailer->Username = null;
    $phpmailer->Password = null;

});

add_filter('wp_mail_from', function () {
    return '<EMAIL>';
});