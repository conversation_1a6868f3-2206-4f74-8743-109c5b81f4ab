<?php

require_once get_parent_theme_file_path('vendor/autoload.php');
require_once 'includes/restricted_access.php';
require_once 'includes/role_for_managing_customers.php';
require_once 'includes/who_is_online.php';

const DD_REGISTER_FORM_CODE_THEME_MOD = 'dd_register_form_shortcode';
const DD_CHILD_THEME_VERSION = '1.0.10';
const DD_B2B_FREE_SHIPPING_INCLUDING_TAX_VALUE = 700.00;

add_action('wp_enqueue_scripts', function () {
    wp_enqueue_style('fitospa-b2b-style', get_stylesheet_uri(), ver: DD_CHILD_THEME_VERSION);
});

add_action('wp_enqueue_scripts', function () {
    wp_enqueue_style('parent-fitospa-b2b-style', get_parent_theme_file_uri( 'style.css' ), ver: DD_CHILD_THEME_VERSION);
    wp_enqueue_style('child-style', get_theme_file_uri( 'assets/css/style.css' ), ver: DD_CHILD_THEME_VERSION);

    if (is_page_template('templates/template-rejestracja.php')) {
        wp_enqueue_script('getresponse-fallback', get_theme_file_uri('assets/js/getresponse-fallback.js'), array(), DD_CHILD_THEME_VERSION, true);
    }
});

/** Ustawienia motywu na stronie */
function dd_theme_settings_child() {
    add_theme_support('dd-product-settings');
    add_action('customize_register', function($wp_customize) {
        $wp_customize->add_section('dd_shortcodes_settings', [
            'title'       => 'Używane shortcode\'y',
            'priority'    => 30,
        ]);

        $wp_customize->add_setting(DD_REGISTER_FORM_CODE_THEME_MOD, [
            'default'   => '',
            'sanitize_callback' => '',
        ]);
        $wp_customize->add_control(DD_REGISTER_FORM_CODE_THEME_MOD, [
            'label'    => 'Shortcode formularza rejestracyjnego',
            'section'  => 'dd_shortcodes_settings',
            'type'     => 'text',
        ]);
    });
}

add_action('after_setup_theme', 'dd_theme_settings_child');
