document.addEventListener('DOMContentLoaded', function() {
    const formContainer = document.querySelector('.b2b-register-form__form');
    
    if (!formContainer) {
        return;
    }

    let checkAttempts = 0;
    const maxAttempts = 50;
    const checkInterval = 200;
    let fallbackShown = false;

    function checkGetResponseForm() {
        checkAttempts++;

        const getResponseForm = formContainer.querySelector('getresponse-form');
        const hasFormContent = formContainer.querySelector('form, iframe, [class*="getresponse"], [id*="getresponse"], [class*="gr-"], [id*="gr-"]');
        const hasVisibleContent = formContainer.offsetHeight > 50 && formContainer.children.length > 0;

        const originalContent = formContainer.innerHTML.trim();
        const hasOnlyOriginalContent = originalContent.length < 200;

        if ((getResponseForm || hasFormContent) && hasVisibleContent && !hasOnlyOriginalContent) {
            return;
        }

        if (checkAttempts >= maxAttempts && !fallbackShown) {
            const cookieConsentExists = typeof Cookiebot !== 'undefined';
            const hasConsent = cookieConsentExists && (Cookiebot.consent.marketing || Cookiebot.consent.statistics || Cookiebot.consent.preferences);

            if (!cookieConsentExists || !hasConsent) {
                showFallbackMessage();
            }
        } else if (checkAttempts < maxAttempts) {
            setTimeout(checkGetResponseForm, checkInterval);
        }
    }

    function showFallbackMessage() {
        if (fallbackShown) return;
        
        fallbackShown = true;
        
        const fallbackHtml = `
            <div class="getresponse-fallback">
                <div class="getresponse-fallback__content">
                    <div class="getresponse-fallback__icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#4A6A65"/>
                        </svg>
                    </div>
                    <h3 class="getresponse-fallback__title">Formularz rejestracji wymaga zgody na ciasteczka</h3>
                    <p class="getresponse-fallback__message">
                        Aby skorzystać z formularza rejestracji, musisz wyrazić zgodę na ciasteczka.
                    </p>
                    <button type="button" class="getresponse-fallback__button" onclick="showCookieConsentDialog()">
                        Zmień zgodę
                    </button>
                </div>
            </div>
        `;
        
        formContainer.innerHTML = fallbackHtml;
    }

    window.showCookieConsentDialog = function() {
        if (typeof Cookiebot !== 'undefined' && Cookiebot.renew) {
            Cookiebot.renew();
        } else if (typeof Cookiebot !== 'undefined' && Cookiebot.show) {
            Cookiebot.show();
        } else {
            console.warn('Cookiebot nie jest dostępny');
            alert('Nie można otworzyć okna zgody na ciasteczka. Sprawdź ustawienia przeglądarki.');
        }
    };

    function initializeCookiebotListeners() {
        if (typeof Cookiebot !== 'undefined') {
            window.addEventListener('CookiebotOnAccept', function() {
                if (fallbackShown) {
                    location.reload();
                }
            });

            window.addEventListener('CookiebotOnDecline', function() {
                if (!fallbackShown) {
                    setTimeout(checkGetResponseForm, 1000);
                }
            });

            window.addEventListener('CookiebotOnConsentReady', function() {
                if (!fallbackShown) {
                    setTimeout(checkGetResponseForm, 500);
                }
            });
        }
    }

    if (typeof Cookiebot !== 'undefined') {
        initializeCookiebotListeners();
    } else {
        window.addEventListener('CookiebotOnLoad', initializeCookiebotListeners);
    }

    setTimeout(checkGetResponseForm, 1000);
});
