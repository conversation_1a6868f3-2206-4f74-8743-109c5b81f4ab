@import "variables";

.main-swipper {
    overflow: hidden;
    background-color: $color-primary;

    .left-right {
        display: flex;
        position: relative;
        gap: 35px;

        .left-side {
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            flex-basis: 65%;
            min-width: 65%;
            padding: 3rem 0;
            color: $color-white;
            order: 1;

            .main-content {
                display: flex;
                flex-direction: column;
                gap: 25px;
                min-height: 455px;
                max-width: 70%;

                .headline {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    hr {
                        width: 40px;
                        margin: 0;
                    }

                    .subtitle {
                        font-style: normal;
                        font-weight: 300;
                        font-size: $font-size-16px;
                        line-height: 32px;
                        text-transform: uppercase;
                    }
                }

                .main-subtitle {
                    margin: 0;

                    hr {
                        width: 40px;
                        margin: 0;
                    }

                    .subtitle {
                        font-style: normal;
                        font-weight: 400;
                        font-size: $font-size-16px;
                        line-height: 32px;
                        text-transform: uppercase;
                    }
                }

                .main-title {
                    font-family: $font-secondary;
                    font-style: normal;
                    margin: 0;
                    font-weight: 400;
                    font-size: $font-size-60px;
                    line-height: 72px;
                    letter-spacing: -0.01em;
                }

                .main-subtitle {
                    font-family: $font-primary;
                    font-style: normal;
                    font-weight: 300;
                    font-size: $font-size-18px;
                    line-height: 155%;
                }

                .main-button {
                    padding: 15px 35px;
                    width: fit-content;
                    transition: $transition;

                    &:hover {
                        border: 1px solid $color-white;
                        color: $color-white;
                        transition: $transition;
                        background-color: $button-hover;
                    }
                }
            }
        }

        .right-side {
            position: fixed;
            top: 0;
            right: 0;
            width: 35vw;
            height: 100%;
            overflow: hidden;
            z-index: -1;
            display: flex;
            margin: 0;

            .right-side__img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center left;
            }

            ul {
                display: flex;
                flex-direction: column;
                gap: 5px;
                position: absolute;
                top: 9%;
                right: 10%;
                padding: 0;
                margin: 0;

                li {
                    list-style-type: none;
                    text-align: right;
                    text-transform: uppercase;
                    color: $color-black;
                    font-size: $font-size-16px;
                }
            }
        }
    }

    .button-wrapper {
        position: absolute;
        gap: 10px;
        right: 38%;
        bottom: 50px;
        z-index: 2;
        display: flex;
        align-items: center;
        cursor: pointer;

        .swiper-pagination,
        .swiper-button {
            width: 45px;
            height: 45px;
        }

        .swiper-progress {
            position: absolute;
            inset: 0;
            border: 3px solid $color-primary;
            border-radius: 50%;
            clip-path: polygon(0 0, 0 0, 0 0, 0 0);
            transform: rotate(-90deg);
            transition: clip-path 1s linear;
        }

        .swiper-pagination {
            font-family: $font-secondary;
            font-size: $font-size-20px;
            order: 2;
            font-weight: 400;
            width: 45px;
            height: 45px;
            border: 1px solid $color-white;
            overflow: hidden;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            position: relative;
            bottom: 0;
            color: $color-white;

            .swiper-pagination-current {
                margin-right: 2px;
                margin-bottom: 5px;
                z-index: 3;
            }

            .swiper-pagination-total {
                margin-left: 2px;
                margin-top: 5px;
            }
        }

        .swiper-button {
            border: 1px solid $color-white;
            border-radius: 50%;
            background-size: 35%;
            background-position: center;
            background-repeat: no-repeat;
            transition: all 0.3s ease-out;
            cursor: pointer;

            svg {
                display: none;
            }

            &:hover {
                background-color: $button-hover;
            }
        }

        .swiper-prev-button {
            order: 1;
            background-image: url(../../public/arrow-left.svg);
        }

        .swiper-next-button {
            order: 3;
            background-image: url(../../public/arrow-right.svg);
        }
    }

    .swiper-slide {
        opacity: 0 !important;
        transition: 0.4s;

        &-active {
            opacity: 1 !important;
        }
    }
}

@media (max-width: $media-tablet) {
    .main-swipper {
        .left-right {
            flex-direction: column-reverse;

            &.dd-container {
                padding: 0;
            }

            .left-side {
                flex-basis: 100%;
                min-width: 100%;
                padding: 2rem 50px;

                .main-content {
                    max-width: 100%;
                    min-height: fit-content;
                    justify-content: center;

                    .main-title {
                        font-size: $font-size-36px;
                        line-height: 1.2;
                    }

                    .main-subtitle {
                        font-size: $font-size-16px;
                    }

                    .main-button {
                        padding: 15px;
                    }
                }
            }

            .right-side {
                display: flex !important;
                position: relative;
                width: 100%;

                .right-side__img {
                    height: 350px;
                }

                ul {
                    top: 8%;
                    right: 8%;
                }
            }
        }

        .button-wrapper {
            right: 50px;
            bottom: 55%;
            height: fit-content;
        }
    }
}

@media (max-width: $media-mobile) {
    .main-swipper {
        .left-right {
            gap: 0;

            .left-side {
                flex-basis: 100%;
                min-width: 100%;
                padding: 3rem 0 4rem 0;

                .main-content {
                    max-width: 100%;
                    margin: 0 auto;
                    padding: 0 15px;

                    .main-title {
                        font-size: $font-size-36px;
                        line-height: 1.2;
                    }

                    .main-subtitle {
                        font-size: $font-size-16px;
                    }

                    .main-button {
                        padding: 15px;
                    }
                }
            }
        }

        .button-wrapper {
            right: 15px;
            bottom: 50%;

            .swiper-button,
            .swiper-pagination {
                width: 35px;
                height: 35px;
            }

            .swiper-pagination {
                font-size: $font-size-18px;
            }
        }
    }
}

@media (max-width: $media-mobile-sm) {
    .main-swipper {
        .button-wrapper {
            bottom: 45%;
        }
    }
}