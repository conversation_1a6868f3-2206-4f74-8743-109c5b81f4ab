.main-swipper {
  overflow: hidden;
  background-color: #4A6A65;
}
.main-swipper .left-right {
  display: flex;
  position: relative;
  gap: 35px;
}
.main-swipper .left-right .left-side {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  flex-basis: 65%;
  min-width: 65%;
  padding: 3rem 0;
  color: #FFFFFF;
  order: 1;
}
.main-swipper .left-right .left-side .main-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
  min-height: 455px;
  max-width: 70%;
}
.main-swipper .left-right .left-side .main-content .headline {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}
.main-swipper .left-right .left-side .main-content .headline hr {
  width: 40px;
  margin: 0;
}
.main-swipper .left-right .left-side .main-content .headline .subtitle {
  font-style: normal;
  font-weight: 300;
  font-size: 1rem;
  line-height: 32px;
  text-transform: uppercase;
}
.main-swipper .left-right .left-side .main-content .main-subtitle {
  margin: 0;
}
.main-swipper .left-right .left-side .main-content .main-subtitle hr {
  width: 40px;
  margin: 0;
}
.main-swipper .left-right .left-side .main-content .main-subtitle .subtitle {
  font-style: normal;
  font-weight: 400;
  font-size: 1rem;
  line-height: 32px;
  text-transform: uppercase;
}
.main-swipper .left-right .left-side .main-content .main-title {
  font-family: "freight-big-pro-light", sans-serif;
  font-style: normal;
  margin: 0;
  font-weight: 400;
  font-size: 3.75rem;
  line-height: 72px;
  letter-spacing: -0.01em;
}
.main-swipper .left-right .left-side .main-content .main-subtitle {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 1.125rem;
  line-height: 155%;
}
.main-swipper .left-right .left-side .main-content .main-button {
  padding: 15px 35px;
  width: fit-content;
  transition: 0.2s ease-out;
}
.main-swipper .left-right .left-side .main-content .main-button:hover {
  border: 1px solid #FFFFFF;
  color: #FFFFFF;
  transition: 0.2s ease-out;
  background-color: rgba(255, 255, 255, 0.1294117647);
}
.main-swipper .left-right .right-side {
  position: fixed;
  top: 0;
  right: 0;
  width: 35vw;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  display: flex;
  margin: 0;
}
.main-swipper .left-right .right-side .right-side__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center left;
}
.main-swipper .left-right .right-side ul {
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: absolute;
  top: 9%;
  right: 10%;
  padding: 0;
  margin: 0;
}
.main-swipper .left-right .right-side ul li {
  list-style-type: none;
  text-align: right;
  text-transform: uppercase;
  color: #000000;
  font-size: 1rem;
}
.main-swipper .button-wrapper {
  position: absolute;
  gap: 10px;
  right: 38%;
  bottom: 50px;
  z-index: 2;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.main-swipper .button-wrapper .swiper-pagination,
.main-swipper .button-wrapper .swiper-button {
  width: 45px;
  height: 45px;
}
.main-swipper .button-wrapper .swiper-progress {
  position: absolute;
  inset: 0;
  border: 3px solid #4A6A65;
  border-radius: 50%;
  clip-path: polygon(0 0, 0 0, 0 0, 0 0);
  transform: rotate(-90deg);
  transition: clip-path 1s linear;
}
.main-swipper .button-wrapper .swiper-pagination {
  font-family: "freight-big-pro-light", sans-serif;
  font-size: 1.25rem;
  order: 2;
  font-weight: 400;
  width: 45px;
  height: 45px;
  border: 1px solid #FFFFFF;
  overflow: hidden;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  bottom: 0;
  color: #FFFFFF;
}
.main-swipper .button-wrapper .swiper-pagination .swiper-pagination-current {
  margin-right: 2px;
  margin-bottom: 5px;
  z-index: 3;
}
.main-swipper .button-wrapper .swiper-pagination .swiper-pagination-total {
  margin-left: 2px;
  margin-top: 5px;
}
.main-swipper .button-wrapper .swiper-button {
  border: 1px solid #FFFFFF;
  border-radius: 50%;
  background-size: 35%;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s ease-out;
  cursor: pointer;
}
.main-swipper .button-wrapper .swiper-button svg {
  display: none;
}
.main-swipper .button-wrapper .swiper-button:hover {
  background-color: rgba(255, 255, 255, 0.1294117647);
}
.main-swipper .button-wrapper .swiper-prev-button {
  order: 1;
  background-image: url(../../public/arrow-left.svg);
}
.main-swipper .button-wrapper .swiper-next-button {
  order: 3;
  background-image: url(../../public/arrow-right.svg);
}
.main-swipper .swiper-slide {
  opacity: 0 !important;
  transition: 0.4s;
}
.main-swipper .swiper-slide-active {
  opacity: 1 !important;
}

@media (max-width: 991.98px) {
  .main-swipper .left-right {
    flex-direction: column-reverse;
  }
  .main-swipper .left-right.dd-container {
    padding: 0;
  }
  .main-swipper .left-right .left-side {
    flex-basis: 100%;
    min-width: 100%;
    padding: 2rem 50px;
  }
  .main-swipper .left-right .left-side .main-content {
    max-width: 100%;
    min-height: fit-content;
    justify-content: center;
  }
  .main-swipper .left-right .left-side .main-content .main-title {
    font-size: 2.25rem;
    line-height: 1.2;
  }
  .main-swipper .left-right .left-side .main-content .main-subtitle {
    font-size: 1rem;
  }
  .main-swipper .left-right .left-side .main-content .main-button {
    padding: 15px;
  }
  .main-swipper .left-right .right-side {
    display: flex !important;
    position: relative;
    width: 100%;
  }
  .main-swipper .left-right .right-side .right-side__img {
    height: 350px;
  }
  .main-swipper .left-right .right-side ul {
    top: 8%;
    right: 8%;
  }
  .main-swipper .button-wrapper {
    right: 50px;
    bottom: 55%;
    height: fit-content;
  }
}
@media (max-width: 767.98px) {
  .main-swipper .left-right {
    gap: 0;
  }
  .main-swipper .left-right .left-side {
    flex-basis: 100%;
    min-width: 100%;
    padding: 3rem 0 4rem 0;
  }
  .main-swipper .left-right .left-side .main-content {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 15px;
  }
  .main-swipper .left-right .left-side .main-content .main-title {
    font-size: 2.25rem;
    line-height: 1.2;
  }
  .main-swipper .left-right .left-side .main-content .main-subtitle {
    font-size: 1rem;
  }
  .main-swipper .left-right .left-side .main-content .main-button {
    padding: 15px;
  }
  .main-swipper .button-wrapper {
    right: 15px;
    bottom: 50%;
  }
  .main-swipper .button-wrapper .swiper-button,
  .main-swipper .button-wrapper .swiper-pagination {
    width: 35px;
    height: 35px;
  }
  .main-swipper .button-wrapper .swiper-pagination {
    font-size: 1.125rem;
  }
}
@media (max-width: 320px) {
  .main-swipper .button-wrapper {
    bottom: 45%;
  }
}

/*# sourceMappingURL=style.css.map */
